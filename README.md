# Chinese-to-English Translation Web Application

A modern, full-stack web application for translating Simplified Chinese text to English using AI-powered translation services.

## 🌟 Features

- **Beautiful, Modern UI**: Clean interface built with React and Tailwind CSS
- **Real-time Translation**: Powered by DeepSeek AI for high-quality translations
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Chinese Font Support**: Proper rendering of Chinese characters
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Copy Functionality**: Easy copying of translated text
- **Character Counting**: Real-time character count for both input and output
- **Loading States**: Visual feedback during translation process

## 🚀 Quick Start

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- DeepSeek API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the project root:
   ```env
   DEEPSEEK_API_KEY=your_deepseek_api_key_here
   PORT=5000
   ```

4. **Start the application**

   **Option 1: Run both frontend and backend together**
   ```bash
   npm run dev
   ```

   **Option 2: Run separately**
   ```bash
   # Terminal 1 - Backend API
   npm run server

   # Terminal 2 - Frontend
   npm start
   ```

5. **Access the application**
   - Frontend: http://localhost:3000 (or next available port)
   - Backend API: http://localhost:5000

## 📁 Project Structure

```
website/
├── public/                 # Static files
├── src/
│   ├── components/
│   │   └── TextTranslator.jsx  # Main translation component
│   ├── App.js             # Root component
│   ├── index.js           # React entry point
│   └── index.css          # Global styles with Tailwind
├── server/
│   └── index.js           # Express backend server
├── .env                   # Environment variables (create this)
├── package.json           # Dependencies and scripts
└── tailwind.config.js     # Tailwind CSS configuration
```

## 🔧 API Endpoints

### POST `/api/translate`
Translates Chinese text to English.

**Request:**
```json
{
  "text": "你好，世界！",
  "from": "zh",
  "to": "en"
}
```

**Response:**
```json
{
  "translatedText": "Hello, world!",
  "sourceLanguage": "zh",
  "targetLanguage": "en",
  "originalLength": 6,
  "translatedLength": 13
}
```

### GET `/api/health`
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-24T22:38:32.489Z",
  "uptime": 19.6043152,
  "environment": "development"
}
```

## 🛠️ Available Scripts

- `npm start` - Start the React frontend
- `npm run server` - Start the Express backend
- `npm run dev` - Start both frontend and backend concurrently
- `npm run build` - Build the React app for production
- `npm test` - Run tests

## 🔐 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `DEEPSEEK_API_KEY` | Your DeepSeek API key | Yes |
| `PORT` | Backend server port (default: 5000) | No |

## 🎨 Technology Stack

**Frontend:**
- React 19
- Tailwind CSS
- Modern JavaScript (ES6+)

**Backend:**
- Node.js
- Express.js
- OpenAI SDK (for DeepSeek API)
- dotenv for environment management

**AI Service:**
- DeepSeek API for translation

## 🔍 Testing

Test the API directly:
```bash
node test-api.js
```

Or use curl:
```bash
curl -X POST http://localhost:5000/api/translate \
  -H "Content-Type: application/json" \
  -d '{"text":"你好，世界！","from":"zh","to":"en"}'
```

## 🚨 Error Handling

The application includes comprehensive error handling for:
- Invalid input validation
- API rate limiting
- Network timeouts
- Authentication errors
- Service unavailability

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)
