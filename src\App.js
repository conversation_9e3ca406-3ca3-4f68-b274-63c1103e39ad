import { useState, useEffect } from 'react';
import logo from './logo.svg';
import './App.css';

function App() {
  const [backendData, setBackendData] = useState({ message: '' });

  useEffect(() => {
    fetch('/api')
      .then(response => response.json())
      .then(data => setBackendData(data))
      .catch(error => console.error('Error fetching data:', error));
  }, []);

  return (
    <div className="App">
      <header className="App-header">
        <img src={logo} className="App-logo" alt="logo" />
        <p>
          {backendData.message || 'Loading...'}
        </p>
        <a
          className="App-link"
          href="https://reactjs.org"
          target="_blank"
          rel="noopener noreferrer"
        >
          Learn React
        </a>
      </header>
    </div>
  );
}

export default App;

