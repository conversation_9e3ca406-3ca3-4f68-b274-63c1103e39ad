// Simple test script for the translation API
const testTranslation = async () => {
  try {
    console.log('Testing translation API...');
    
    const response = await fetch('http://localhost:5000/api/translate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: '你好，世界！',
        from: 'zh',
        to: 'en'
      }),
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Translation successful!');
      console.log('Original:', '你好，世界！');
      console.log('Translation:', data.translatedText);
      console.log('Full response:', data);
    } else {
      console.log('❌ Translation failed:');
      console.log('Status:', response.status);
      console.log('Error:', data);
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
};

// Test health endpoint
const testHealth = async () => {
  try {
    console.log('\nTesting health endpoint...');
    const response = await fetch('http://localhost:5000/api/health');
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Health check passed!');
      console.log('Status:', data.status);
      console.log('Uptime:', Math.round(data.uptime), 'seconds');
    } else {
      console.log('❌ Health check failed');
    }
  } catch (error) {
    console.log('❌ Health check error:', error.message);
  }
};

// Run tests
const runTests = async () => {
  await testHealth();
  await testTranslation();
};

runTests();
